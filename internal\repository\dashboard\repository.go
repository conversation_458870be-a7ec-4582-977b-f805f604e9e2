package dashboard

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Reader interface defines read operations for dashboard entities
type Reader interface {
	// Unified FinancialMap operations
	FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error)

	// IncomeSource operations
	FindIncomeSource(ctx context.Context, id primitive.ObjectID) (*dashboard.IncomeSource, error)
	FindIncomeSources(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error)

	// EmergencyFund operations
	FindEmergencyFund(ctx context.Context, userID string) (*dashboard.EmergencyFund, error)

	// Investment operations
	FindInvestment(ctx context.Context, id primitive.ObjectID) (*dashboard.Investment, error)
	FindInvestments(ctx context.Context, userID string) ([]*dashboard.Investment, error)

	// Asset operations
	FindAsset(ctx context.Context, id primitive.ObjectID) (*dashboard.Asset, error)
	FindAssets(ctx context.Context, userID string) ([]*dashboard.Asset, error)

	// NetWorthSnapshot operations
	FindNetWorthHistory(ctx context.Context, userID string, limit int) ([]*dashboard.NetWorthSnapshot, error)
}

// Writer interface defines write operations for dashboard entities
type Writer interface {
	// Unified FinancialMap operations
	SaveFinancialMap(ctx context.Context, financialMap *dashboard.FinancialMap) error
	UpdateFinancialMap(ctx context.Context, financialMap *dashboard.FinancialMap) error

	// IncomeSource operations
	CreateIncomeSource(ctx context.Context, incomeSource *dashboard.IncomeSource) error
	UpdateIncomeSource(ctx context.Context, incomeSource *dashboard.IncomeSource) error
	DeleteIncomeSource(ctx context.Context, id primitive.ObjectID) error

	// EmergencyFund operations
	CreateEmergencyFund(ctx context.Context, emergencyFund *dashboard.EmergencyFund) error
	UpdateEmergencyFund(ctx context.Context, emergencyFund *dashboard.EmergencyFund) error

	// Investment operations
	CreateInvestment(ctx context.Context, investment *dashboard.Investment) error
	UpdateInvestment(ctx context.Context, investment *dashboard.Investment) error
	DeleteInvestment(ctx context.Context, id primitive.ObjectID) error

	// Asset operations
	CreateAsset(ctx context.Context, asset *dashboard.Asset) error
	UpdateAsset(ctx context.Context, asset *dashboard.Asset) error
	DeleteAsset(ctx context.Context, id primitive.ObjectID) error

	// NetWorthSnapshot operations
	SaveNetWorthSnapshot(ctx context.Context, snapshot *dashboard.NetWorthSnapshot) error
}

// Repository interface combines Reader and Writer interfaces
type Repository interface {
	Reader
	Writer
}
