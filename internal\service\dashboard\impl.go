package dashboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

// Implementation is in service.go - this file contains the method implementations

// FindFinancialMap implements the unified approach for dashboard data
func (s *service) FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error) {
	// Try to fetch from unified collection
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return nil, err
	}

	// If found, update with live income data and return
	if financialMap != nil {
		return s.updateFinancialMapWithLiveData(ctx, financialMap)
	}

	// If not found, create a new financial map with live data
	return s.createNewFinancialMap(ctx, userID)
}

// updateFinancialMapWithLiveData updates the financial map with live income data from financial sheet
func (s *service) updateFinancialMapWithLiveData(ctx context.Context, financialMap *dashboard.FinancialMap) (*dashboard.FinancialMap, error) {
	// Fetch live income sources from financial sheet transactions
	incomeSources, err := s.aggregateIncomeSourcesFromTransactions(ctx, financialMap.UserID)
	if err != nil {
		return nil, err
	}

	// Update income sources and monthly income
	financialMap.IncomeSources = make([]dashboard.IncomeSource, 0, len(incomeSources))
	var monthlyIncome monetary.Amount
	for _, source := range incomeSources {
		financialMap.IncomeSources = append(financialMap.IncomeSources, *source)
		monthlyIncome += source.MonthlyAmount
	}
	financialMap.MonthlyIncome = monthlyIncome

	// Create dynamic snapshot for current month
	var emergencyFundValue monetary.Amount
	if financialMap.EmergencyFund != nil {
		emergencyFundValue = financialMap.EmergencyFund.CurrentValue
	}

	currentSnapshot := dashboard.NetWorthSnapshot{
		UserID:             financialMap.UserID,
		Date:               time.Now(),
		EmergencyFundValue: emergencyFundValue,
		InvestmentsValue:   financialMap.TotalInvestments,
		AssetsValue:        financialMap.TotalAssets,
		TotalValue:         emergencyFundValue + financialMap.TotalInvestments + financialMap.TotalAssets,
	}

	// Add current snapshot to history if not already present for current month
	financialMap.NetWorthHistory = s.addCurrentSnapshotToHistory(financialMap.NetWorthHistory, currentSnapshot)

	return financialMap, nil
}

// createNewFinancialMap creates a new financial map with live data
func (s *service) createNewFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error) {
	// Fetch live income sources from financial sheet transactions
	incomeSources, err := s.aggregateIncomeSourcesFromTransactions(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Calculate monthly income
	var monthlyIncome monetary.Amount
	incomeSourcesSlice := make([]dashboard.IncomeSource, 0, len(incomeSources))
	for _, source := range incomeSources {
		incomeSourcesSlice = append(incomeSourcesSlice, *source)
		monthlyIncome += source.MonthlyAmount
	}

	// Create new financial map
	financialMap := &dashboard.FinancialMap{
		UserID:           userID,
		MonthlyIncome:    monthlyIncome,
		EmergencyFund:    nil, // Will be nil until user sets it
		TotalInvestments: 0,
		TotalAssets:      0,
		NetWorthHistory:  []dashboard.NetWorthSnapshot{},
		IncomeSources:    incomeSourcesSlice,
		Investments:      []dashboard.Investment{},
		Assets:           []dashboard.Asset{},
	}

	// Prepare for creation
	if err := financialMap.PrepareCreate(); err != nil {
		return nil, err
	}

	// Save to database
	if err := s.Repository.SaveFinancialMap(ctx, financialMap); err != nil {
		return nil, err
	}

	return financialMap, nil
}

// addCurrentSnapshotToHistory adds the current snapshot to history if not already present for current month
func (s *service) addCurrentSnapshotToHistory(history []dashboard.NetWorthSnapshot, currentSnapshot dashboard.NetWorthSnapshot) []dashboard.NetWorthSnapshot {
	now := time.Now()
	currentYear, currentMonth, _ := now.Date()

	// Check if we already have a snapshot for the current month
	for i, snapshot := range history {
		snapshotYear, snapshotMonth, _ := snapshot.Date.Date()
		if snapshotYear == currentYear && snapshotMonth == currentMonth {
			// Update existing snapshot with current data
			history[i] = currentSnapshot
			return history
		}
	}

	// Add current snapshot to the end
	return append(history, currentSnapshot)
}

// aggregateIncomeSourcesFromTransactions fetches income transactions and aggregates by money source
func (s *service) aggregateIncomeSourcesFromTransactions(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error) {
	// Get current year and month
	now := time.Now()
	currentYear := now.Year()
	currentMonth := int(now.Month())

	// Fetch all income transactions for the current month
	transactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, financialsheet.CategoryTypeIncome, currentYear, currentMonth)
	if err != nil {
		return nil, err
	}

	// Group transactions by money source (keep latest transaction per source)
	sourceMap := make(map[financialsheet.MoneySource]*financialsheet.Transaction)
	for _, transaction := range transactions {
		existing, exists := sourceMap[transaction.MoneySource]
		if !exists || transaction.Date.After(existing.Date) {
			sourceMap[transaction.MoneySource] = transaction
		}
	}

	// Convert to IncomeSource objects
	var incomeSources []*dashboard.IncomeSource
	for moneySource, transaction := range sourceMap {
		// Get category information
		category, err := s.FinancialSheetService.FindCategoryByIdentifier(ctx, transaction.Category)
		if err != nil {
			continue // Skip if category not found
		}

		incomeSource := &dashboard.IncomeSource{
			UserID:        userID,
			Name:          category.Name,
			MonthlyAmount: transaction.Value,
			MoneySource:   moneySource,
			Icon:          category.Icon,
			LastUpdated:   transaction.Date,
		}

		incomeSources = append(incomeSources, incomeSource)
	}

	return incomeSources, nil
}

// UpdateEmergencyFund updates the emergency fund in the user's financial map
func (s *service) UpdateEmergencyFund(ctx context.Context, userID string, currentValue monetary.Amount) error {
	// Get existing financial map or create new one
	financialMap, err := s.getOrCreateFinancialMap(ctx, userID)
	if err != nil {
		return err
	}

	// Update emergency fund
	if financialMap.EmergencyFund == nil {
		financialMap.EmergencyFund = &dashboard.EmergencyFund{
			UserID:       userID,
			CurrentValue: currentValue,
			GoalValue:    0, // Default goal value
		}
		if err := financialMap.EmergencyFund.PrepareCreate(); err != nil {
			return err
		}
	} else {
		financialMap.EmergencyFund.CurrentValue = currentValue
		if err := financialMap.EmergencyFund.PrepareUpdate(); err != nil {
			return err
		}
	}

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}

// UpdateEmergencyFundGoal updates the emergency fund goal in the user's financial map
func (s *service) UpdateEmergencyFundGoal(ctx context.Context, userID string, goalValue monetary.Amount) error {
	// Get existing financial map or create new one
	financialMap, err := s.getOrCreateFinancialMap(ctx, userID)
	if err != nil {
		return err
	}

	// Update emergency fund goal
	if financialMap.EmergencyFund == nil {
		financialMap.EmergencyFund = &dashboard.EmergencyFund{
			UserID:       userID,
			CurrentValue: 0, // Default current value
			GoalValue:    goalValue,
		}
		if err := financialMap.EmergencyFund.PrepareCreate(); err != nil {
			return err
		}
	} else {
		financialMap.EmergencyFund.GoalValue = goalValue
		if err := financialMap.EmergencyFund.PrepareUpdate(); err != nil {
			return err
		}
	}

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}

// getOrCreateFinancialMap gets an existing financial map or creates a new one
func (s *service) getOrCreateFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error) {
	// Try to get existing financial map
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return nil, err
	}

	// If found, return it
	if financialMap != nil {
		return financialMap, nil
	}

	// If not found, create a new one
	return s.createNewFinancialMap(ctx, userID)
}

// AddInvestment adds a new investment to the user's financial map
func (s *service) AddInvestment(ctx context.Context, userID string, name string, currentValue monetary.Amount) error {
	// Get existing financial map or create new one
	financialMap, err := s.getOrCreateFinancialMap(ctx, userID)
	if err != nil {
		return err
	}

	// Create new investment
	investment := dashboard.Investment{
		UserID:       userID,
		Name:         name,
		CurrentValue: currentValue,
	}

	if err := investment.PrepareCreate(); err != nil {
		return err
	}

	// Add to financial map
	financialMap.Investments = append(financialMap.Investments, investment)

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}
